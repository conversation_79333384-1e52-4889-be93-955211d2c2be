{"sysTime":"2025-08-05 10:58:29.263","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-08-05 10:58:29.257","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:29.305","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:29.479","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:29.482","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-08-05 10:58:29.482","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-08-05 10:58:29.483","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-08-05 10:58:29.505","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-08-05 10:58:29.511","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-08-05 10:58:29.571","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:29.580","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-08-05 10:58:29.592","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-08-05 10:58:31.150","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:31.158","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=delivery-mysql.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:31.161","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=delivery-mysql.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:31.163","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-08-05 10:58:31.165","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=delivery-mysql.yaml, group=apply, tenant=ets-dev, config=spring:\\n  autoconfigure:\\n    #自动化配置 例外处理\\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.Dr...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:32.559","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:32.560","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=delivery-config.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:32.561","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=delivery-config.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:32.561","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=delivery-config.yaml, group=apply, tenant=ets-dev, config=delivery-config:\\n  default-storage-code: Yunda \\n  default-express-corp-code: YUNDA\\n  default-logisti...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:33.860","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:33.863","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=common-starter-config.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:33.865","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=common-starter-config.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:33.866","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=common-starter-config.yaml, group=apply, tenant=ets-dev, config=spring:\\n  redis:\\n    host: ************\\n    password: pWd@123yes\\n    port: 6379\\n    database: 15\\n  d...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:35.163","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:35.165","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=yunda-config.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:35.165","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=yunda-config.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:35.167","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=yunda-config.yaml, group=apply, tenant=ets-dev, config=yunda:\\n  appKey: 21177924\\n  customerId: YWKH000275\\n  warehouseCode: CK031\\n  format: xml\\n  signMethod...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:36.475","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:36.476","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=kd100-config.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:36.477","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=kd100-config.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:36.479","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=kd100-config.yaml, group=apply, tenant=ets-dev, config=kd100:\\n  appKey: \\\"omHFcvuP7026\\\"\\n  customer: \\\"A28D4B9D6BD6304A74EE0C2185351244\\\"\\n  schema: json\\n  resu...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:38.165","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:38.167","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=jd-config.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:38.168","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=jd-config.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:38.169","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=jd-config.yaml, group=apply, tenant=ets-dev, config=jd:\\n  apiUrl: https://api.jdl.com\\n  accessToken: b66dffbcebc74515ae8e31d1de871bce\\n  appKey: 2723d005...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:39.465","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:39.468","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=apply-cos-tencent.properties, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:39.469","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=apply-cos-tencent.properties, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:39.470","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=apply-cos-tencent.properties, group=apply, tenant=ets-dev, config=cos.tencent.secretId=AKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA\\ncos.tencent.secretKey=mLMDsoDTVL0X7a4eN7iM...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:40.974","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:40.976","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=delivery-application, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:40.976","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=delivery-application, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:40.977","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=delivery-application, group=apply, tenant=ets-dev, config=\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:40.977","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:42.269","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:42.270","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=delivery-application.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:42.271","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=delivery-application.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:42.272","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=delivery-application.yaml, group=apply, tenant=ets-dev, config=server:\\n  port: 20130\\n  servlet:\\n    session:\\n      cookie:\\n        http-only: false\\n\\nspring:\\n  main...\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:43.637","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-dev.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-dev.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:43.639","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=delivery-application-dev.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-dev.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-dev.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-08-05 10:58:43.640","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=delivery-application-dev.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-dev.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:43.640","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=delivery-application-dev.yaml, group=apply, tenant=ets-dev, config=\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:43.640","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-dev.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:43.642","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-dev.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-08-05 10:58:43.658","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:43.660","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"dev\"","thrown":""}
{"sysTime":"2025-08-05 10:58:44.710","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-08-05 10:58:44.712","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-08-05 10:58:44.740","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-08-05 10:58:44.981","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=bdd8b8d6-5232-3524-a862-57be56ca2783","thrown":""}
{"sysTime":"2025-08-05 10:58:45.236","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.242","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.244","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$838/0x0000007001704e78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.247","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.276","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.285","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.292","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.320","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.323","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:58:45.555","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-08-05 10:58:45.561","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-08-05 10:58:45.565","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-08-05 10:58:45.565","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-08-05 10:58:45.594","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-08-05 10:58:45.594","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1913 ms","thrown":""}
{"sysTime":"2025-08-05 10:58:47.719","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-08-05 10:58:49.061","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-08-05 10:58:50.773","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-08-05 10:58:52.455","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-08-05 10:58:52.456","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-08-05 10:58:52.456","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-08-05 10:58:52.456","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-08-05 10:58:52.456","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-08-05 10:58:52.457","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [4] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-08-05 10:58:53.317","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:58:53.389","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:58:55.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-08-05 10:58:55.474","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-08-05 10:58:57.330","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-08-05 10:58:57.770","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:58:57.772","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:58:57.809","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-08-05 10:58:57.809","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-08-05 10:58:57.809","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-08-05 10:58:57.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-08-05 10:58:59.126","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:00.418","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:01.712","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:01.714","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:01.714","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.nacos.client.naming:357","methodName":"com.alibaba.nacos.client.naming:updateServiceNow-357","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:355)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:330)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:143)\n\tat com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:465)\n\tat com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:454)\n"}
{"sysTime":"2025-08-05 10:59:01.964","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:01.967","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:01.970","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:01.979","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:632","methodName":"o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:refresh-632","message":"{\"msg\":\"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'defaultExpressConsumer' defined in class path resource [com/ets/delivery/application/common/config/queue/express/QueueExpressConfig.class]: Failed to instantiate [org.apache.rocketmq.client.consumer.DefaultMQPushConsumer]: Factory method 'defaultExpressConsumer' threw exception with message: org.apache.rocketmq.remoting.exception.RemotingConnectException: connect to [name-service:9876] failed\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:01.981","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:203","methodName":"c.xxl.job.core.executor.XxlJobExecutor:stopRpcProvider-203","message":"{\"msg\":\"Cannot invoke \\\"com.xxl.rpc.remoting.provider.XxlRpcProviderFactory.stop()\\\" because \\\"this.xxlRpcProviderFactory\\\" is null\"}","thrown":"java.lang.NullPointerException: Cannot invoke \"com.xxl.rpc.remoting.provider.XxlRpcProviderFactory.stop()\" because \"this.xxlRpcProviderFactory\" is null\n\tat com.xxl.job.core.executor.XxlJobExecutor.stopRpcProvider(XxlJobExecutor.java:201)\n\tat com.xxl.job.core.executor.XxlJobExecutor.destroy(XxlJobExecutor.java:88)\n\tat com.xxl.job.core.executor.impl.XxlJobSpringExecutor.destroy(XxlJobSpringExecutor.java:48)\n\tat org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:211)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)\n\tat org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1202)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)\n\tat org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1195)\n\tat org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1186)\n"}
{"sysTime":"2025-08-05 10:59:01.993","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.994","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.994","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.994","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.994","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.994","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.994","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.995","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.996","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.c.a.AnnotationConfigApplicationContext:1133","methodName":"o.s.c.a.AnnotationConfigApplicationContext:doClose-1133","message":"{\"msg\":\"Exception thrown from ApplicationListener handling ContextClosedEvent\"}","thrown":"org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'applicationTaskExecutor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:265)\n\tat org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:222)\n\tat org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:451)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:457)\n\tat org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:384)\n\tat org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1130)\n"}
{"sysTime":"2025-08-05 10:59:01.999","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-08-05 10:59:02.001","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-08-05 10:59:02.005","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-08-05 10:59:02.005","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-08-05 10:59:02.005","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-08-05 10:59:02.005","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-08-05 10:59:02.005","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-08-05 10:59:02.005","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-08-05 10:59:02.006","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-08-05 10:59:02.006","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
{"sysTime":"2025-08-05 10:59:02.006","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Stopping service [Tomcat]","thrown":""}
{"sysTime":"2025-08-05 10:59:02.015","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.s.b.a.l.ConditionEvaluationReportLogger:82","methodName":"o.s.b.a.l.ConditionEvaluationReportLogger:logMessage-82","message":"\n\nError starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.","thrown":""}
{"sysTime":"2025-08-05 10:59:02.026","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"main","className":"o.springframework.boot.SpringApplication:851","methodName":"o.springframework.boot.SpringApplication:reportFailure-851","message":"{\"msg\":\"Application run failed\"}","thrown":"org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'defaultExpressConsumer' defined in class path resource [com/ets/delivery/application/common/config/queue/express/QueueExpressConfig.class]: Failed to instantiate [org.apache.rocketmq.client.consumer.DefaultMQPushConsumer]: Factory method 'defaultExpressConsumer' threw exception with message: org.apache.rocketmq.remoting.exception.RemotingConnectException: connect to [name-service:9876] failed\n\tat org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648)\n\tat org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)\nCaused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.rocketmq.client.consumer.DefaultMQPushConsumer]: Factory method 'defaultExpressConsumer' threw exception with message: org.apache.rocketmq.remoting.exception.RemotingConnectException: connect to [name-service:9876] failed\n\tat org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)\n\tat org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:644)\n\tat org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)\n\tat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)\n\tat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)\n\tat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)\nCaused by: java.lang.IllegalStateException: org.apache.rocketmq.remoting.exception.RemotingConnectException: connect to [name-service:9876] failed\n\tat org.apache.rocketmq.client.impl.factory.MQClientInstance.updateTopicRouteInfoFromNameServer(MQClientInstance.java:680)\n\tat org.apache.rocketmq.client.impl.factory.MQClientInstance.updateTopicRouteInfoFromNameServer(MQClientInstance.java:510)\n\tat org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl.updateTopicSubscribeInfoWhenSubscriptionChanged(DefaultMQPushConsumerImpl.java:872)\n\tat org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl.start(DefaultMQPushConsumerImpl.java:655)\n\tat org.apache.rocketmq.client.consumer.DefaultMQPushConsumer.start(DefaultMQPushConsumer.java:707)\n\tat com.ets.common.queue.QueueBaseConfig.consumerStart(QueueBaseConfig.java:81)\n\tat com.ets.common.queue.QueueBaseConfig.consumerStart(QueueBaseConfig.java:101)\n\tat com.ets.delivery.application.common.config.queue.express.QueueExpressConfig.defaultExpressConsumer(QueueExpressConfig.java:39)\n\tat com.ets.delivery.application.common.config.queue.express.QueueExpressConfig$$SpringCGLIB$$1.CGLIB$defaultExpressConsumer$5(<generated>)\n\tat com.ets.delivery.application.common.config.queue.express.QueueExpressConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)\nCaused by: org.apache.rocketmq.remoting.exception.RemotingConnectException: connect to [name-service:9876] failed\n\tat org.apache.rocketmq.remoting.netty.NettyRemotingClient.getAndCreateNameserverChannel(NettyRemotingClient.java:458)\n\tat org.apache.rocketmq.remoting.netty.NettyRemotingClient.getAndCreateChannel(NettyRemotingClient.java:413)\n\tat org.apache.rocketmq.remoting.netty.NettyRemotingClient.invokeSync(NettyRemotingClient.java:382)\n\tat org.apache.rocketmq.client.impl.MQClientAPIImpl.getTopicRouteInfoFromNameServer(MQClientAPIImpl.java:1394)\n\tat org.apache.rocketmq.client.impl.MQClientAPIImpl.getTopicRouteInfoFromNameServer(MQClientAPIImpl.java:1384)\n\tat org.apache.rocketmq.client.impl.factory.MQClientInstance.updateTopicRouteInfoFromNameServer(MQClientInstance.java:623)\n\tat org.apache.rocketmq.client.impl.factory.MQClientInstance.updateTopicRouteInfoFromNameServer(MQClientInstance.java:510)\n\tat org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl.updateTopicSubscribeInfoWhenSubscriptionChanged(DefaultMQPushConsumerImpl.java:872)\n\tat org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl.start(DefaultMQPushConsumerImpl.java:655)\n\tat org.apache.rocketmq.client.consumer.DefaultMQPushConsumer.start(DefaultMQPushConsumer.java:707)\n"}
{"sysTime":"2025-08-05 10:59:03.995","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:05.309","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:07.045","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:07.046","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:07.046","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-08-05 10:59:10.341","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:11.647","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:13.049","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:13.050","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:13.050","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-08-05 10:59:18.332","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:19.643","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:21.124","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-08-05 10:59:21.125","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:21.125","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=ets-dev&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=60821&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-08-05 10:59:22.824","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:22.824","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:22.824","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8159","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:24.534","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-08-05 10:59:24.513","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:24.646","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:24.882","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:24.884","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-08-05 10:59:24.885","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-08-05 10:59:24.886","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-08-05 10:59:24.899","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-08-05 10:59:24.911","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-08-05 10:59:24.962","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:24.970","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-08-05 10:59:24.981","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-08-05 10:59:25.200","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-08-05 10:59:25.468","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:25.540","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-dev.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:25.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-dev.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-08-05 10:59:25.546","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:25.547","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"dev\"","thrown":""}
{"sysTime":"2025-08-05 10:59:26.162","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-08-05 10:59:26.163","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-08-05 10:59:26.183","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-08-05 10:59:26.354","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=bdd8b8d6-5232-3524-a862-57be56ca2783","thrown":""}
{"sysTime":"2025-08-05 10:59:26.560","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.562","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.564","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$836/0x00000070017214c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.566","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.579","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.584","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.589","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.607","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.609","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:26.755","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-08-05 10:59:26.760","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-08-05 10:59:26.762","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-08-05 10:59:26.762","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-08-05 10:59:26.800","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-08-05 10:59:26.800","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1242 ms","thrown":""}
{"sysTime":"2025-08-05 10:59:29.062","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-08-05 10:59:30.414","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-08-05 10:59:32.039","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-08-05 10:59:33.781","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-08-05 10:59:33.782","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-08-05 10:59:33.782","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-08-05 10:59:33.782","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-08-05 10:59:33.782","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-08-05 10:59:33.783","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [4] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-08-05 10:59:34.649","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:34.708","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:36.025","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-08-05 10:59:36.205","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-08-05 10:59:37.851","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-08-05 10:59:38.290","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.292","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.326","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-08-05 10:59:38.327","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-08-05 10:59:38.327","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-08-05 10:59:38.333","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-08-05 10:59:38.428","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-08-05 10:59:38.431","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-08-05 10:59:38.648","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.651","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.653","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.826","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_express_group, nameServerAddr=name-service:9876, topic=ets_express_topic, tag=queueExpress","thrown":""}
{"sysTime":"2025-08-05 10:59:38.844","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_review_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.847","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:38.850","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.028","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_review_group, nameServerAddr=name-service:9876, topic=ets_review_topic, tag=queueReview","thrown":""}
{"sysTime":"2025-08-05 10:59:39.038","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_delivery_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.042","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.045","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.250","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_delivery_group, nameServerAddr=name-service:9876, topic=ets_java_delivery_task_topic, tag=queueTask","thrown":""}
{"sysTime":"2025-08-05 10:59:39.338","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-08-05 10:59:39.418","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.559","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-delivery, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-08-05 10:59:39.572","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_risk_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.575","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.578","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 10:59:39.755","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_risk_group, nameServerAddr=name-service:9876, topic=ets_java_risk_task_topic, tag=queueRiskTask","thrown":""}
{"sysTime":"2025-08-05 10:59:40.423","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-08-05 10:59:40.439","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 21 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-08-05 10:59:40.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Starting ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.555","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:241","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:start-241","message":"Tomcat started on port 20130 (http) with context path ''","thrown":""}
{"sysTime":"2025-08-05 10:59:40.556","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:81","methodName":"com.alibaba.nacos.client.naming:addBeatInfo-81","message":"[BEAT] adding beat: BeatInfo{port=20130, ip='************', weight=1.0, serviceName='apply@@delivery-application', cluster='DEFAULT', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.","thrown":""}
{"sysTime":"2025-08-05 10:59:40.556","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] ets-dev registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-08-05 10:59:40.590","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.registry.NacosServiceRegistry:76","methodName":"c.a.c.n.registry.NacosServiceRegistry:register-76","message":"nacos registry, apply delivery-application ************:20130 register finished","thrown":""}
{"sysTime":"2025-08-05 10:59:40.631","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.e.d.application.DeliveryApplication:56","methodName":"c.e.d.application.DeliveryApplication:logStarted-56","message":"Started DeliveryApplication in 16.763 seconds (process running for 17.169)","thrown":""}
{"sysTime":"2025-08-05 10:59:40.635","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] common-starter-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.635","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=common-starter-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.635","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=common-starter-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application-dev.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application-dev.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application-dev.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] jd-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=jd-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=jd-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] yunda-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=yunda-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=yunda-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] kd100-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=kd100-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=kd100-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-mysql.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-mysql.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 10:59:40.638","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-mysql.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 10:59:40.645","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryAfterSalesReviewsNotifyHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@765e4ccc[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#retryAfterSalesReviewsNotifyHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:manualNotifyAfterSalesReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4ef6a2fd[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#manualNotifyAfterSalesReviewHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:erpOrderDailyCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7dcc5e9d[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#erpOrderDailyCheckHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixErpOrderHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4611fe60[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#fixErpOrderHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:expressSubscribe, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42adf242[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#expressSubscribe]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:LogisticsQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@393751a0[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#logisticsQueryHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaLogisticsExpressQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2690d8ad[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#yundaLogisticsExpressQueryHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixLogisticsExpressStatusHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f3bd0e1[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#fixLogisticsExpressStatusHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpAutoCancelHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@422910a6[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpAutoCancelHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpQueryTraceInfoHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4110e312[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpQueryTraceInfoHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:postReviewReleaseHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@61650e2d[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#postReviewReleaseHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:initPostReviewDataHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@50e86638[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#initPostReviewDataHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewDateSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5acee0a3[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewDateSummaryHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewUserSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1c00809b[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewUserSummaryHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyReviewOrderRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@555df150[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#applyReviewOrderRiskHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:releaseUserRiskReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@46978465[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#releaseUserRiskReviewHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:checkOvertimeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6bf1bfec[class com.ets.delivery.application.app.job.SendBackJob$$SpringCGLIB$$0#checkOvertimeHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:logisticsAvgHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@73edfe51[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#logisticsAvgHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.647","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:goodsStockHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4980460d[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#goodsStockHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.648","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageAlarmFileHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33ce57f4[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#storageAlarmFileHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.648","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:stockOutSkuHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@55a690be[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#stockOutSkuHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.648","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageMapAddressCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4925e32d[class com.ets.delivery.application.app.job.StorageMapJob$$SpringCGLIB$$0#storageMa1pAddressCheck]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.648","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2de742a[class com.ets.delivery.application.app.job.TaskJob$$SpringCGLIB$$0#reExecHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.681","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45566129[class com.ets.risk.application.app.job.RiskTaskJob$$SpringCGLIB$$0#reExecRiskHandler]","thrown":""}
{"sysTime":"2025-08-05 10:59:40.789","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"o.s.c.openfeign.FeignClientFactoryBean:468","methodName":"o.s.c.openfeign.FeignClientFactoryBean:getTarget-468","message":"For 'base-application' URL not provided. Will try picking an instance via load-balancing.","thrown":""}
{"sysTime":"2025-08-05 10:59:40.961","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"main","className":"c.x.r.r.provider.XxlRpcProviderFactory:197","methodName":"c.x.r.r.provider.XxlRpcProviderFactory:addService-197","message":">>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl","thrown":""}
{"sysTime":"2025-08-05 10:59:40.969","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-35","className":"com.xxl.rpc.remoting.net.Server:66","methodName":"com.xxl.rpc.remoting.net.Server:run-66","message":">>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 20132","thrown":""}
{"sysTime":"2025-08-05 10:59:40.983","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"RMI TCP Connection(8)-127.0.0.1","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-08-05 10:59:40.983","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"RMI TCP Connection(8)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:532","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-532","message":"Initializing Servlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-08-05 10:59:40.984","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"RMI TCP Connection(8)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:554","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-554","message":"Completed initialization in 1 ms","thrown":""}
{"sysTime":"2025-08-05 10:59:41.298","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79514550A60E002A","thrown":""}
{"sysTime":"2025-08-05 10:59:41.508","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79515F0770B200D4","thrown":""}
{"sysTime":"2025-08-05 10:59:41.704","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51C53FE7B0219","thrown":""}
{"sysTime":"2025-08-05 10:59:41.905","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54131708F003B","thrown":""}
{"sysTime":"2025-08-05 10:59:42.108","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5789FF4B30076","thrown":""}
{"sysTime":"2025-08-05 10:59:42.302","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B582E933DD0006","thrown":""}
{"sysTime":"2025-08-05 10:59:42.496","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5986BFC510095","thrown":""}
{"sysTime":"2025-08-05 10:59:42.691","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56FB8D05106B9","thrown":""}
{"sysTime":"2025-08-05 10:59:42.896","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51BEF3A030211","thrown":""}
{"sysTime":"2025-08-05 10:59:43.086","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51C34AED20255","thrown":""}
{"sysTime":"2025-08-05 10:59:43.283","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51C3F655B0269","thrown":""}
{"sysTime":"2025-08-05 10:59:43.482","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51CDED0F20011","thrown":""}
{"sysTime":"2025-08-05 10:59:43.677","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B583E15BA40033","thrown":""}
{"sysTime":"2025-08-05 10:59:43.877","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B584F572040061","thrown":""}
{"sysTime":"2025-08-05 10:59:44.072","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B516FB4CD00302","thrown":""}
{"sysTime":"2025-08-05 10:59:44.264","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51810139603B1","thrown":""}
{"sysTime":"2025-08-05 10:59:44.466","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5221B3B2800C4","thrown":""}
{"sysTime":"2025-08-05 10:59:44.658","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B531A4815A0342","thrown":""}
{"sysTime":"2025-08-05 10:59:44.850","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B535F793E50519","thrown":""}
{"sysTime":"2025-08-05 10:59:45.048","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5417FFDF50021","thrown":""}
{"sysTime":"2025-08-05 10:59:45.233","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B559AEA30201D5","thrown":""}
{"sysTime":"2025-08-05 10:59:45.430","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5696816F50234","thrown":""}
{"sysTime":"2025-08-05 10:59:45.624","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59CFEC4310049","thrown":""}
{"sysTime":"2025-08-05 10:59:45.821","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59E3BB69C0142","thrown":""}
{"sysTime":"2025-08-05 10:59:46.016","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59EED535601C9","thrown":""}
{"sysTime":"2025-08-05 10:59:46.212","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B508D9A9EE0050","thrown":""}
{"sysTime":"2025-08-05 10:59:46.402","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50D02931400E4","thrown":""}
{"sysTime":"2025-08-05 10:59:46.604","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53600CF95044F","thrown":""}
{"sysTime":"2025-08-05 10:59:46.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5368A69DB04D3","thrown":""}
{"sysTime":"2025-08-05 10:59:46.999","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54A8C86130002","thrown":""}
{"sysTime":"2025-08-05 10:59:47.248","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B55AEAAE63002F","thrown":""}
{"sysTime":"2025-08-05 10:59:47.441","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5700FF633001A","thrown":""}
{"sysTime":"2025-08-05 10:59:47.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79513C3A6C66003E","thrown":""}
{"sysTime":"2025-08-05 10:59:47.829","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795140E077D7009D","thrown":""}
{"sysTime":"2025-08-05 10:59:48.026","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795146C6EFEB0005","thrown":""}
{"sysTime":"2025-08-05 10:59:48.221","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79516610ED38002E","thrown":""}
{"sysTime":"2025-08-05 10:59:48.419","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795169913F140015","thrown":""}
{"sysTime":"2025-08-05 10:59:48.617","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79516A56AF350003","thrown":""}
{"sysTime":"2025-08-05 10:59:48.809","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51D9B16BC0049","thrown":""}
{"sysTime":"2025-08-05 10:59:49.002","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51DC154F00064","thrown":""}
{"sysTime":"2025-08-05 10:59:49.196","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B551379D6E0147","thrown":""}
{"sysTime":"2025-08-05 10:59:49.386","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5601A9C26001A","thrown":""}
{"sysTime":"2025-08-05 10:59:49.511","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-08-05 10:59:49.511","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='************#20130#DEFAULT#apply@@delivery-application', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}, Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-08-05 10:59:49.512","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(2) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000},{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-08-05 10:59:49.603","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B599394EEA0005","thrown":""}
{"sysTime":"2025-08-05 10:59:49.796","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522694C2E0509","thrown":""}
{"sysTime":"2025-08-05 10:59:49.994","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522C96DD8056E","thrown":""}
{"sysTime":"2025-08-05 10:59:50.194","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52E8CFA7E04C3","thrown":""}
{"sysTime":"2025-08-05 10:59:50.389","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5502CB5990073","thrown":""}
{"sysTime":"2025-08-05 10:59:50.584","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B565C5EE5B00E4","thrown":""}
{"sysTime":"2025-08-05 10:59:50.779","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51C08E5460223","thrown":""}
{"sysTime":"2025-08-05 10:59:50.972","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B540C5A5BC004E","thrown":""}
{"sysTime":"2025-08-05 10:59:51.162","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58965E6680113","thrown":""}
{"sysTime":"2025-08-05 10:59:51.359","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50E298BB500E9","thrown":""}
{"sysTime":"2025-08-05 10:59:51.569","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B517C305BB036B","thrown":""}
{"sysTime":"2025-08-05 10:59:51.772","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51CFBE54A04D7","thrown":""}
{"sysTime":"2025-08-05 10:59:51.986","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5210B617E0113","thrown":""}
{"sysTime":"2025-08-05 10:59:52.183","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53C8DBB2E0002","thrown":""}
{"sysTime":"2025-08-05 10:59:52.378","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B541319E2A0133","thrown":""}
{"sysTime":"2025-08-05 10:59:52.574","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B55F1846640295","thrown":""}
{"sysTime":"2025-08-05 10:59:52.771","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B560186B290014","thrown":""}
{"sysTime":"2025-08-05 10:59:52.969","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56463FE5D02ED","thrown":""}
{"sysTime":"2025-08-05 10:59:53.167","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57DDA2B95004A","thrown":""}
{"sysTime":"2025-08-05 10:59:53.364","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58EC3B818002F","thrown":""}
{"sysTime":"2025-08-05 10:59:53.557","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59DFF542200F4","thrown":""}
{"sysTime":"2025-08-05 10:59:53.773","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B508B7AD46003A","thrown":""}
{"sysTime":"2025-08-05 10:59:53.968","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:AC15854400017DAF6ECC3C782EBD002A","thrown":""}
{"sysTime":"2025-08-05 10:59:54.168","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:AC156CF500017DAF6ECC6A306BE50002","thrown":""}
{"sysTime":"2025-08-05 10:59:54.365","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:AC156CF500017DAF6ECC6A6C20570009","thrown":""}
{"sysTime":"2025-08-05 10:59:54.567","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:AC156CEC00017DAF6ECC74B055D000CF","thrown":""}
{"sysTime":"2025-08-05 10:59:54.764","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B7951233E568A000D","thrown":""}
{"sysTime":"2025-08-05 10:59:54.982","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795126701FC00000","thrown":""}
{"sysTime":"2025-08-05 10:59:55.184","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795126FF2BD90011","thrown":""}
{"sysTime":"2025-08-05 10:59:55.384","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79513BDAA07F0031","thrown":""}
{"sysTime":"2025-08-05 10:59:55.582","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B7951403D6C4E007D","thrown":""}
{"sysTime":"2025-08-05 10:59:55.783","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79514069F8C50096","thrown":""}
{"sysTime":"2025-08-05 10:59:55.978","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795169812DDD0006","thrown":""}
{"sysTime":"2025-08-05 10:59:56.178","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53720588D0004","thrown":""}
{"sysTime":"2025-08-05 10:59:56.378","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B55ADE9A1D0051","thrown":""}
{"sysTime":"2025-08-05 10:59:56.586","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56413C0F30003","thrown":""}
{"sysTime":"2025-08-05 10:59:56.788","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B574DD509C0009","thrown":""}
{"sysTime":"2025-08-05 10:59:56.990","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57ED4FB920069","thrown":""}
{"sysTime":"2025-08-05 10:59:57.233","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5984638E40077","thrown":""}
{"sysTime":"2025-08-05 10:59:57.436","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51D4AD59E0112","thrown":""}
{"sysTime":"2025-08-05 10:59:57.636","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52B45E6B3014B","thrown":""}
{"sysTime":"2025-08-05 10:59:57.842","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54BB561AF01B5","thrown":""}
{"sysTime":"2025-08-05 10:59:58.042","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5695AD0ED03C9","thrown":""}
{"sysTime":"2025-08-05 10:59:58.235","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56E717CD60568","thrown":""}
{"sysTime":"2025-08-05 10:59:58.443","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B574D5428E0C2C","thrown":""}
{"sysTime":"2025-08-05 10:59:58.639","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51C13219B022F","thrown":""}
{"sysTime":"2025-08-05 10:59:58.831","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51CE315360016","thrown":""}
{"sysTime":"2025-08-05 10:59:59.026","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5402BA5720007","thrown":""}
{"sysTime":"2025-08-05 10:59:59.215","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58439030A0044","thrown":""}
{"sysTime":"2025-08-05 10:59:59.432","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51345374D0250","thrown":""}
{"sysTime":"2025-08-05 10:59:59.637","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5179E74560345","thrown":""}
{"sysTime":"2025-08-05 10:59:59.832","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5229A8C020059","thrown":""}
{"sysTime":"2025-08-05 11:00:00.032","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5323DD21B0444","thrown":""}
{"sysTime":"2025-08-05 11:00:00.238","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53BE28BCA0262","thrown":""}
{"sysTime":"2025-08-05 11:00:00.452","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B7951375345B80043","thrown":""}
{"sysTime":"2025-08-05 11:00:00.653","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79513BF7D9B80039","thrown":""}
{"sysTime":"2025-08-05 11:00:00.861","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79513D1BED1F0057","thrown":""}
{"sysTime":"2025-08-05 11:00:01.058","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79513D3FEBEA005E","thrown":""}
{"sysTime":"2025-08-05 11:00:01.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B79514046C0E00082","thrown":""}
{"sysTime":"2025-08-05 11:00:01.448","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F0000010001443B795141D595E10004","thrown":""}
{"sysTime":"2025-08-05 11:00:01.654","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51D74FDB3003C","thrown":""}
{"sysTime":"2025-08-05 11:00:01.914","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59909FEAC000C","thrown":""}
{"sysTime":"2025-08-05 11:00:02.150","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B530CDE34305BE","thrown":""}
{"sysTime":"2025-08-05 11:00:02.354","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5454273980293","thrown":""}
{"sysTime":"2025-08-05 11:00:02.557","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56944532F03A7","thrown":""}
{"sysTime":"2025-08-05 11:00:02.754","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51BD049A20204","thrown":""}
{"sysTime":"2025-08-05 11:00:02.950","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50CA00AC30025","thrown":""}
{"sysTime":"2025-08-05 11:00:03.161","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50D9BBBF90078","thrown":""}
{"sysTime":"2025-08-05 11:00:03.368","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5171DA9C80320","thrown":""}
{"sysTime":"2025-08-05 11:00:03.564","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5181A4A7203CD","thrown":""}
{"sysTime":"2025-08-05 11:00:03.757","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51CB4AD3F04AA","thrown":""}
{"sysTime":"2025-08-05 11:00:03.969","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522E37D3B00DA","thrown":""}
{"sysTime":"2025-08-05 11:00:04.168","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53610CFC4054E","thrown":""}
{"sysTime":"2025-08-05 11:00:04.369","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5367DC4650035","thrown":""}
{"sysTime":"2025-08-05 11:00:04.588","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54610A8A90044","thrown":""}
{"sysTime":"2025-08-05 11:00:04.787","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B555DE4DB30065","thrown":""}
{"sysTime":"2025-08-05 11:00:04.983","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5693F164A021B","thrown":""}
{"sysTime":"2025-08-05 11:00:05.183","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5896F251B009B","thrown":""}
{"sysTime":"2025-08-05 11:00:05.394","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59EC80ABC01B2","thrown":""}
{"sysTime":"2025-08-05 11:00:05.595","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59ED2A74201BC","thrown":""}
{"sysTime":"2025-08-05 11:00:05.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50D5F5F200005","thrown":""}
{"sysTime":"2025-08-05 11:00:06.023","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5262FA5AE042E","thrown":""}
{"sysTime":"2025-08-05 11:00:06.219","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5359A470F03BB","thrown":""}
{"sysTime":"2025-08-05 11:00:06.425","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B535B7B46703E7","thrown":""}
{"sysTime":"2025-08-05 11:00:06.653","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B537A25A2F0204","thrown":""}
{"sysTime":"2025-08-05 11:00:06.860","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B537AD766E0218","thrown":""}
{"sysTime":"2025-08-05 11:00:07.124","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B578D9F33A0139","thrown":""}
{"sysTime":"2025-08-05 11:00:07.347","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5978F26A403AA","thrown":""}
{"sysTime":"2025-08-05 11:00:07.555","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5265BC83600CE","thrown":""}
{"sysTime":"2025-08-05 11:00:07.756","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52BA2037E02E5","thrown":""}
{"sysTime":"2025-08-05 11:00:07.956","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52C75AB0D03B6","thrown":""}
{"sysTime":"2025-08-05 11:00:08.162","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52CF52B2A044E","thrown":""}
{"sysTime":"2025-08-05 11:00:08.354","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5510D705C0563","thrown":""}
{"sysTime":"2025-08-05 11:00:08.555","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52B5C134E00A2","thrown":""}
{"sysTime":"2025-08-05 11:00:08.779","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53C52599F036E","thrown":""}
{"sysTime":"2025-08-05 11:00:08.978","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5400BFE8A0030","thrown":""}
{"sysTime":"2025-08-05 11:00:09.181","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B589261FA40073","thrown":""}
{"sysTime":"2025-08-05 11:00:09.369","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5896E01470100","thrown":""}
{"sysTime":"2025-08-05 11:00:09.598","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B502FE357D01A0","thrown":""}
{"sysTime":"2025-08-05 11:00:09.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B509307ACB0015","thrown":""}
{"sysTime":"2025-08-05 11:00:10.010","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50C8ED0170180","thrown":""}
{"sysTime":"2025-08-05 11:00:10.207","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B521F054B0059C","thrown":""}
{"sysTime":"2025-08-05 11:00:10.430","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5229BA227067B","thrown":""}
{"sysTime":"2025-08-05 11:00:10.658","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522A8F93C069F","thrown":""}
{"sysTime":"2025-08-05 11:00:10.861","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522CD4DA806DE","thrown":""}
{"sysTime":"2025-08-05 11:00:11.063","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B530A169220095","thrown":""}
{"sysTime":"2025-08-05 11:00:11.259","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5460732FA0089","thrown":""}
{"sysTime":"2025-08-05 11:00:11.470","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54646A4DD0128","thrown":""}
{"sysTime":"2025-08-05 11:00:11.669","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57E094A7701C6","thrown":""}
{"sysTime":"2025-08-05 11:00:11.883","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F95062E0110","thrown":""}
{"sysTime":"2025-08-05 11:00:12.080","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58EC701E802C0","thrown":""}
{"sysTime":"2025-08-05 11:00:12.289","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"EventJobBean\",\"className\":\"com.ets.starter.disposer.EventDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"eventClassName\":\"\",\"params\":{\"orderSn\":\"250804170000011818A2\"},\"eventBeanName\":\"orderActivatedEventBean\"},\"retry\":0,\"spanId\":\"0.8.1\",\"traceId\":\"68907a38cae04\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-08-05 11:00:12.490","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50CA6828300AE","thrown":""}
{"sysTime":"2025-08-05 11:00:12.689","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50D468CD600F9","thrown":""}
{"sysTime":"2025-08-05 11:00:12.902","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522D3DD72039A","thrown":""}
{"sysTime":"2025-08-05 11:00:13.106","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5308E89CC093A","thrown":""}
{"sysTime":"2025-08-05 11:00:13.304","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53751D8080171","thrown":""}
{"sysTime":"2025-08-05 11:00:13.511","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B546B59AC600AA","thrown":""}
{"sysTime":"2025-08-05 11:00:13.715","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B546D6AF840109","thrown":""}
{"sysTime":"2025-08-05 11:00:13.939","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5698BAFC20154","thrown":""}
{"sysTime":"2025-08-05 11:00:14.215","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5744B9CFA00B8","thrown":""}
{"sysTime":"2025-08-05 11:00:14.456","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B578E9984602F3","thrown":""}
{"sysTime":"2025-08-05 11:00:14.671","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B597E0139E0032","thrown":""}
{"sysTime":"2025-08-05 11:00:14.874","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59889201800C4","thrown":""}
{"sysTime":"2025-08-05 11:00:15.080","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54B0FA69A0037","thrown":""}
{"sysTime":"2025-08-05 11:00:15.303","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B569880FF31569","thrown":""}
{"sysTime":"2025-08-05 11:00:15.521","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B50CD60D720008","thrown":""}
{"sysTime":"2025-08-05 11:00:15.727","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52CD3F0B50030","thrown":""}
{"sysTime":"2025-08-05 11:00:15.950","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53C77CD0003C2","thrown":""}
{"sysTime":"2025-08-05 11:00:16.161","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B541B761770053","thrown":""}
{"sysTime":"2025-08-05 11:00:16.364","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5608FD58D0008","thrown":""}
{"sysTime":"2025-08-05 11:00:16.579","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5898FD21D0128","thrown":""}
{"sysTime":"2025-08-05 11:00:16.823","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5899770280140","thrown":""}
{"sysTime":"2025-08-05 11:00:17.090","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52768893300D4","thrown":""}
{"sysTime":"2025-08-05 11:00:17.313","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52C8E90690045","thrown":""}
{"sysTime":"2025-08-05 11:00:17.516","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52CD8C1AB0069","thrown":""}
{"sysTime":"2025-08-05 11:00:17.726","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B530C9C73000D6","thrown":""}
{"sysTime":"2025-08-05 11:00:17.933","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53744790E02BE","thrown":""}
{"sysTime":"2025-08-05 11:00:18.146","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57E15B65001D4","thrown":""}
{"sysTime":"2025-08-05 11:00:18.359","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57ED9A011000A","thrown":""}
{"sysTime":"2025-08-05 11:00:18.582","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F19DE560050","thrown":""}
{"sysTime":"2025-08-05 11:00:18.796","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F6E292300C2","thrown":""}
{"sysTime":"2025-08-05 11:00:19.011","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D656685005D","thrown":""}
{"sysTime":"2025-08-05 11:00:19.242","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D7689130078","thrown":""}
{"sysTime":"2025-08-05 11:00:19.457","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53C9E3B970011","thrown":""}
{"sysTime":"2025-08-05 11:00:19.664","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53CB9FF4B0024","thrown":""}
{"sysTime":"2025-08-05 11:00:19.884","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5400FECF10038","thrown":""}
{"sysTime":"2025-08-05 11:00:20.104","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5405109310034","thrown":""}
{"sysTime":"2025-08-05 11:00:20.338","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B546B549360002","thrown":""}
{"sysTime":"2025-08-05 11:00:20.577","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56099234E00C2","thrown":""}
{"sysTime":"2025-08-05 11:00:20.809","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B565C0CA06005A","thrown":""}
{"sysTime":"2025-08-05 11:00:21.035","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57E8FEACA0099","thrown":""}
{"sysTime":"2025-08-05 11:00:21.247","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59D73C65E0068","thrown":""}
{"sysTime":"2025-08-05 11:00:21.461","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59F9BFFE501D9","thrown":""}
{"sysTime":"2025-08-05 11:00:21.712","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5030571AF001E","thrown":""}
{"sysTime":"2025-08-05 11:00:21.957","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5086931A60062","thrown":""}
{"sysTime":"2025-08-05 11:00:22.187","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5328F8D6F029F","thrown":""}
{"sysTime":"2025-08-05 11:00:22.404","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B535B7F59203E8","thrown":""}
{"sysTime":"2025-08-05 11:00:22.598","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B537D1E753023D","thrown":""}
{"sysTime":"2025-08-05 11:00:22.799","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5461213CA008F","thrown":""}
{"sysTime":"2025-08-05 11:00:22.994","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B574C6A0E3004F","thrown":""}
{"sysTime":"2025-08-05 11:00:23.189","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D78F2C001AC","thrown":""}
{"sysTime":"2025-08-05 11:00:23.393","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5266752A900E1","thrown":""}
{"sysTime":"2025-08-05 11:00:23.592","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52756EE6601B7","thrown":""}
{"sysTime":"2025-08-05 11:00:23.790","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53C088888004E","thrown":""}
{"sysTime":"2025-08-05 11:00:23.988","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54005F0D40252","thrown":""}
{"sysTime":"2025-08-05 11:00:24.184","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5404D85FD031E","thrown":""}
{"sysTime":"2025-08-05 11:00:24.391","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D6710120473","thrown":""}
{"sysTime":"2025-08-05 11:00:24.593","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51D4C36AE006E","thrown":""}
{"sysTime":"2025-08-05 11:00:24.790","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B51D60543B008E","thrown":""}
{"sysTime":"2025-08-05 11:00:24.988","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B550DE545102A0","thrown":""}
{"sysTime":"2025-08-05 11:00:25.190","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58486642E001D","thrown":""}
{"sysTime":"2025-08-05 11:00:25.386","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5027A8324013F","thrown":""}
{"sysTime":"2025-08-05 11:00:25.591","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B530D1A8F400E7","thrown":""}
{"sysTime":"2025-08-05 11:00:25.789","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57DF9BFE901B2","thrown":""}
{"sysTime":"2025-08-05 11:00:25.994","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F51A7ED00AD","thrown":""}
{"sysTime":"2025-08-05 11:00:26.194","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B537B95B1F0230","thrown":""}
{"sysTime":"2025-08-05 11:00:26.389","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54534D9A10064","thrown":""}
{"sysTime":"2025-08-05 11:00:26.613","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5466B84AF0141","thrown":""}
{"sysTime":"2025-08-05 11:00:26.837","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B598C6AE92011F","thrown":""}
{"sysTime":"2025-08-05 11:00:27.037","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52690880D011A","thrown":""}
{"sysTime":"2025-08-05 11:00:27.292","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52B837BAB02B8","thrown":""}
{"sysTime":"2025-08-05 11:00:27.482","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52B89E5A602C6","thrown":""}
{"sysTime":"2025-08-05 11:00:27.681","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52BB429B602FE","thrown":""}
{"sysTime":"2025-08-05 11:00:27.888","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54036DD5E02DC","thrown":""}
{"sysTime":"2025-08-05 11:00:28.094","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54A5B0FEB0397","thrown":""}
{"sysTime":"2025-08-05 11:00:28.293","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54B1E870F0045","thrown":""}
{"sysTime":"2025-08-05 11:00:28.518","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52CEF885F005C","thrown":""}
{"sysTime":"2025-08-05 11:00:28.732","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B52D0DC82E00BF","thrown":""}
{"sysTime":"2025-08-05 11:00:28.933","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5375652180151","thrown":""}
{"sysTime":"2025-08-05 11:00:29.133","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B53C51248B036B","thrown":""}
{"sysTime":"2025-08-05 11:00:29.336","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B54120C2970087","thrown":""}
{"sysTime":"2025-08-05 11:00:29.540","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B550AB9E8F026F","thrown":""}
{"sysTime":"2025-08-05 11:00:29.745","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5547693F500BB","thrown":""}
{"sysTime":"2025-08-05 11:00:29.946","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5894C9F1B00BF","thrown":""}
{"sysTime":"2025-08-05 11:00:30.142","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58960C83600E6","thrown":""}
{"sysTime":"2025-08-05 11:00:30.343","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B502750A010137","thrown":""}
{"sysTime":"2025-08-05 11:00:30.548","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B522AAEC9506A4","thrown":""}
{"sysTime":"2025-08-05 11:00:30.750","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B530ABFD6200AB","thrown":""}
{"sysTime":"2025-08-05 11:00:30.962","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B530B2078300BA","thrown":""}
{"sysTime":"2025-08-05 11:00:31.158","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B550AF00030110","thrown":""}
{"sysTime":"2025-08-05 11:00:31.354","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B56E92F15B0069","thrown":""}
{"sysTime":"2025-08-05 11:00:31.550","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57DE87946018B","thrown":""}
{"sysTime":"2025-08-05 11:00:31.744","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57E0F783D01CB","thrown":""}
{"sysTime":"2025-08-05 11:00:31.952","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F1323900042","thrown":""}
{"sysTime":"2025-08-05 11:00:32.151","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F9117D60109","thrown":""}
{"sysTime":"2025-08-05 11:00:32.344","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D6F47F70070","thrown":""}
{"sysTime":"2025-08-05 11:00:32.546","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D71FDD80073","thrown":""}
{"sysTime":"2025-08-05 11:00:32.741","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58E4E405901A5","thrown":""}
{"sysTime":"2025-08-05 11:00:32.935","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59CDA604E026D","thrown":""}
{"sysTime":"2025-08-05 11:00:33.135","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59E4B333F04F5","thrown":""}
{"sysTime":"2025-08-05 11:00:33.334","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5936A4AC70110","thrown":""}
{"sysTime":"2025-08-05 11:00:33.528","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F79EECC00D1","thrown":""}
{"sysTime":"2025-08-05 11:00:33.735","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B57F897EBE00F2","thrown":""}
{"sysTime":"2025-08-05 11:00:33.932","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B58D792C51007C","thrown":""}
{"sysTime":"2025-08-05 11:00:34.135","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5934CBCA700DC","thrown":""}
{"sysTime":"2025-08-05 11:00:34.329","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B5990022AA00CE","thrown":""}
{"sysTime":"2025-08-05 11:00:34.533","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59DB8835B03B3","thrown":""}
{"sysTime":"2025-08-05 11:00:34.733","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"68907a38cae04","spanId":"0.8.1","parentId":"0.8","exportable":"","pid":"8252","thread":"ConsumeMessageThread_ets-group-event-delivery_1","className":"com.ets.starter.base.BaseConsumer:138","methodName":"com.ets.starter.base.BaseConsumer:handleRocketMq-138","message":"MQ消息已过期超过172800秒，直接返回成功，messageId:7F000001000105EF04B59DF9C4BF041C","thrown":""}
{"sysTime":"2025-08-05 11:03:12.267","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"54516d0c-0a62-4d9a-b0fc-b91709bb5468","spanId":"0","parentId":"","exportable":"","pid":"8252","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"062bfb3536eb897b20e3f217298dea6d\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-select-option\",\"params\":{}}","thrown":""}
{"sysTime":"2025-08-05 11:03:12.404","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"54516d0c-0a62-4d9a-b0fc-b91709bb5468","spanId":"0","parentId":"","exportable":"","pid":"8252","thread":"http-nio-20130-exec-1","className":"c.e.d.a.common.micro.CommonInterceptor:36","methodName":"c.e.d.a.common.micro.CommonInterceptor:preHandle-36","message":"{\"userId\":\"\",\"url\":\"/admin/risk-reviews/get-select-option\",\"msg\":\"用户不存在\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:12.425","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"54516d0c-0a62-4d9a-b0fc-b91709bb5468","spanId":"0","parentId":"","exportable":"","pid":"8252","thread":"http-nio-20130-exec-1","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-1,\"msg\":\"用户不存在\",\"data\":null}","thrown":""}
{"sysTime":"2025-08-05 11:03:27.882","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:27.882","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:27.884","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:27.885","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:27.901","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:95","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-95","message":"De-registering from Nacos Server now...","thrown":""}
{"sysTime":"2025-08-05 11:03:27.901","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:100","methodName":"com.alibaba.nacos.client.naming:removeBeatInfo-100","message":"[BEAT] removing beat: apply@@delivery-application:************:20130 from beat map.","thrown":""}
{"sysTime":"2025-08-05 11:03:27.901","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:268","methodName":"com.alibaba.nacos.client.naming:deregisterService-268","message":"[DEREGISTER-SERVICE] ets-dev deregistering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}","thrown":""}
{"sysTime":"2025-08-05 11:03:27.964","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:115","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-115","message":"De-registration finished.","thrown":""}
{"sysTime":"2025-08-05 11:03:27.965","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:03:28.897","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:03:28.898","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:03:30.520","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:248","methodName":"com.alibaba.nacos.client.naming:processServiceJson-248","message":"removed ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-08-05 11:03:30.521","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-08-05 11:03:30.522","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-08-05 11:03:30.523","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:03:33.539","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:136","methodName":"com.alibaba.nacos.client.naming:shutdown-136","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:03:33.540","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:03:33.540","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:134","methodName":"com.alibaba.nacos.client.naming:shutdown-134","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:03:33.540","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:428","methodName":"com.alibaba.nacos.client.naming:shutdown-428","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:03:33.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:783","methodName":"com.alibaba.nacos.client.naming:shutdown-783","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:03:33.541","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:72","methodName":"com.alibaba.nacos.client.naming:shutdown-72","message":"{\"msg\":\"[NamingHttpClientManager] Start destroying NacosRestTemplate\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:33.541","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:79","methodName":"com.alibaba.nacos.client.naming:shutdown-79","message":"{\"msg\":\"[NamingHttpClientManager] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:33.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialWatcher:105","methodName":"c.a.n.client.identify.CredentialWatcher:stop-105","message":"[null] CredentialWatcher is stopped","thrown":""}
{"sysTime":"2025-08-05 11:03:33.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialService:98","methodName":"c.a.n.client.identify.CredentialService:free-98","message":"[null] CredentialService is freed","thrown":""}
{"sysTime":"2025-08-05 11:03:33.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:787","methodName":"com.alibaba.nacos.client.naming:shutdown-787","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:03:34.590","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-35","className":"com.xxl.rpc.remoting.net.Server:74","methodName":"com.xxl.rpc.remoting.net.Server:run-74","message":">>>>>>>>>>> xxl-rpc remoting server stop.","thrown":""}
{"sysTime":"2025-08-05 11:03:34.711","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:87","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-87","message":">>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='etc-java-delivery-executor-dev', registryValue='**********:20132'}, registryResult:ReturnT [code=200, msg=null, content=null]","thrown":""}
{"sysTime":"2025-08-05 11:03:34.711","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:105","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-105","message":">>>>>>>>>>> xxl-job, executor registry thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:03:34.711","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.xxl.rpc.remoting.net.Server:110","methodName":"com.xxl.rpc.remoting.net.Server:stop-110","message":">>>>>>>>>>> xxl-rpc remoting server destroy success.","thrown":""}
{"sysTime":"2025-08-05 11:03:34.712","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"xxl-job, executor TriggerCallbackThread","className":"c.x.j.core.thread.TriggerCallbackThread:96","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-96","message":">>>>>>>>>>> xxl-job, executor callback thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:03:34.712","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"Thread-34","className":"c.x.j.core.thread.TriggerCallbackThread:126","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-126","message":">>>>>>>>>>> xxl-job, executor retry callback thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:03:34.924","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-08-05 11:03:34.925","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:03:34.927","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-08-05 11:03:34.927","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:03:34.927","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-08-05 11:03:34.927","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:03:34.927","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-08-05 11:03:34.928","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:03:34.928","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-08-05 11:03:34.928","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8252","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
{"sysTime":"2025-08-05 11:03:39.213","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-08-05 11:03:39.207","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:39.255","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:39.423","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:39.425","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-08-05 11:03:39.425","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-08-05 11:03:39.426","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-08-05 11:03:39.447","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-08-05 11:03:39.456","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-08-05 11:03:39.529","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:39.572","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-08-05 11:03:39.674","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-08-05 11:03:40.012","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-08-05 11:03:40.676","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:40.780","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-test.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:40.781","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-test.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-08-05 11:03:40.787","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:40.787","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"test\"","thrown":""}
{"sysTime":"2025-08-05 11:03:41.595","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-08-05 11:03:41.596","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-08-05 11:03:41.620","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-08-05 11:03:41.820","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=bdd8b8d6-5232-3524-a862-57be56ca2783","thrown":""}
{"sysTime":"2025-08-05 11:03:42.062","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.065","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.066","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$836/0x000000700171f180] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.069","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.084","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.091","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.096","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.118","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.121","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-08-05 11:03:42.309","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-08-05 11:03:42.317","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-08-05 11:03:42.320","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-08-05 11:03:42.320","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-08-05 11:03:42.362","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-08-05 11:03:42.362","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1563 ms","thrown":""}
{"sysTime":"2025-08-05 11:03:45.998","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-08-05 11:03:48.684","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-08-05 11:03:51.763","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-08-05 11:03:55.312","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-08-05 11:03:55.312","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-08-05 11:03:55.312","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-08-05 11:03:55.313","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-08-05 11:03:55.313","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-08-05 11:03:55.313","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [4] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-08-05 11:03:56.228","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:03:56.298","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:03:57.949","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-08-05 11:03:58.231","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-08-05 11:04:00.982","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-08-05 11:04:01.521","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:01.529","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:01.573","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-08-05 11:04:01.573","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-08-05 11:04:01.574","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-08-05 11:04:01.582","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-08-05 11:04:01.742","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-08-05 11:04:01.748","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-08-05 11:04:02.022","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.025","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.027","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.385","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_express_group, nameServerAddr=name-service:9876, topic=ets_express_topic, tag=queueExpress","thrown":""}
{"sysTime":"2025-08-05 11:04:02.405","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_review_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.409","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.412","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.588","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-08-05 11:04:02.703","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_review_group, nameServerAddr=name-service:9876, topic=ets_review_topic, tag=queueReview","thrown":""}
{"sysTime":"2025-08-05 11:04:02.712","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_delivery_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.715","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:02.718","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:03.144","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_delivery_group, nameServerAddr=name-service:9876, topic=ets_java_delivery_task_topic, tag=queueTask","thrown":""}
{"sysTime":"2025-08-05 11:04:03.345","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:03.617","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-delivery, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-08-05 11:04:03.642","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_risk_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:03.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:03.650","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-08-05 11:04:03.938","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_risk_group, nameServerAddr=name-service:9876, topic=ets_java_risk_task_topic, tag=queueRiskTask","thrown":""}
{"sysTime":"2025-08-05 11:04:04.891","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-08-05 11:04:04.905","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 21 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-08-05 11:04:05.131","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Starting ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.137","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:241","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:start-241","message":"Tomcat started on port 20130 (http) with context path ''","thrown":""}
{"sysTime":"2025-08-05 11:04:05.139","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:81","methodName":"com.alibaba.nacos.client.naming:addBeatInfo-81","message":"[BEAT] adding beat: BeatInfo{port=20130, ip='************', weight=1.0, serviceName='apply@@delivery-application', cluster='DEFAULT', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.","thrown":""}
{"sysTime":"2025-08-05 11:04:05.140","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] test registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-08-05 11:04:05.191","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.registry.NacosServiceRegistry:76","methodName":"c.a.c.n.registry.NacosServiceRegistry:register-76","message":"nacos registry, apply delivery-application ************:20130 register finished","thrown":""}
{"sysTime":"2025-08-05 11:04:05.244","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.e.d.application.DeliveryApplication:56","methodName":"c.e.d.application.DeliveryApplication:logStarted-56","message":"Started DeliveryApplication in 26.424 seconds (process running for 26.849)","thrown":""}
{"sysTime":"2025-08-05 11:04:05.249","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] common-starter-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.249","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=common-starter-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.249","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=common-starter-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.250","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] jd-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.250","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=jd-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.250","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=jd-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.250","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-application-test.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.251","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-application-test.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application-test.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-application+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-application, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] yunda-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=yunda-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.252","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=yunda-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.253","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] kd100-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.253","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=kd100-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.253","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=kd100-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-application.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-application.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-mysql.yaml+apply+test","thrown":""}
{"sysTime":"2025-08-05 11:04:05.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-mysql.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-08-05 11:04:05.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-mysql.yaml, group=apply","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryAfterSalesReviewsNotifyHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@78444c50[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#retryAfterSalesReviewsNotifyHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:manualNotifyAfterSalesReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@28a9112c[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#manualNotifyAfterSalesReviewHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:erpOrderDailyCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@316b57e0[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#erpOrderDailyCheckHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixErpOrderHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b950f8c[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#fixErpOrderHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:expressSubscribe, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@51e9f462[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#expressSubscribe]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaLogisticsExpressQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3129bf02[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#yundaLogisticsExpressQueryHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixLogisticsExpressStatusHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5a963a90[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#fixLogisticsExpressStatusHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.261","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:LogisticsQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@795cb721[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#logisticsQueryHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpQueryTraceInfoHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6788168c[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpQueryTraceInfoHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpAutoCancelHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1b2574d0[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpAutoCancelHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:postReviewReleaseHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@708c9f9e[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#postReviewReleaseHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:initPostReviewDataHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6cf5cb11[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#initPostReviewDataHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewDateSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4b6e9c75[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewDateSummaryHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewUserSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7b5de23c[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewUserSummaryHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyReviewOrderRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f39e2a6[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#applyReviewOrderRiskHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.266","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:releaseUserRiskReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2dd61113[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#releaseUserRiskReviewHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.266","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:checkOvertimeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1264bde8[class com.ets.delivery.application.app.job.SendBackJob$$SpringCGLIB$$0#checkOvertimeHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.266","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:logisticsAvgHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@63ce19a[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#logisticsAvgHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.266","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:goodsStockHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@26300f29[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#goodsStockHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.267","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageAlarmFileHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b73cc75[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#storageAlarmFileHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.267","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:stockOutSkuHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@c4fad0e[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#stockOutSkuHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.267","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageMapAddressCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6d1ead2c[class com.ets.delivery.application.app.job.StorageMapJob$$SpringCGLIB$$0#storageMa1pAddressCheck]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.267","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4b71aee1[class com.ets.delivery.application.app.job.TaskJob$$SpringCGLIB$$0#reExecHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.296","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@44243d32[class com.ets.risk.application.app.job.RiskTaskJob$$SpringCGLIB$$0#reExecRiskHandler]","thrown":""}
{"sysTime":"2025-08-05 11:04:05.443","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"o.s.c.openfeign.FeignClientFactoryBean:468","methodName":"o.s.c.openfeign.FeignClientFactoryBean:getTarget-468","message":"For 'base-application' URL not provided. Will try picking an instance via load-balancing.","thrown":""}
{"sysTime":"2025-08-05 11:04:05.598","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"main","className":"c.x.r.r.provider.XxlRpcProviderFactory:197","methodName":"c.x.r.r.provider.XxlRpcProviderFactory:addService-197","message":">>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl","thrown":""}
{"sysTime":"2025-08-05 11:04:05.607","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-37","className":"com.xxl.rpc.remoting.net.Server:66","methodName":"com.xxl.rpc.remoting.net.Server:run-66","message":">>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 20132","thrown":""}
{"sysTime":"2025-08-05 11:04:05.676","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"RMI TCP Connection(7)-127.0.0.1","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-08-05 11:04:05.676","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"RMI TCP Connection(7)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:532","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-532","message":"Initializing Servlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-08-05 11:04:05.680","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"RMI TCP Connection(7)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:554","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-554","message":"Completed initialization in 3 ms","thrown":""}
{"sysTime":"2025-08-05 11:04:12.078","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"7951e7b9-bcec-4f4a-835f-9d9a26cecf9c","spanId":"0","parentId":"","exportable":"","pid":"8445","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"21f80b68883fc1a873fffed5b8e6611d\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-select-option\",\"params\":{}}","thrown":""}
{"sysTime":"2025-08-05 11:04:12.678","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"7951e7b9-bcec-4f4a-835f-9d9a26cecf9c","spanId":"0","parentId":"","exportable":"","pid":"8445","thread":"http-nio-20130-exec-1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=RiskFeign, name=RiskFeign, url=http://delivery-application:20130)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/risk/getRuleByParams\\\\\\\"}, produces={}, value={\\\\\\\"/risk/getRuleByParams\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"queryDTO\\\"]\",\"args\":\"[{\\\"businessType\\\":1,\\\"ruleType\\\":3}]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":[{\\\"ruleId\\\":10,\\\"ruleName\\\":\\\"风控5\\\"},{\\\"ruleId\\\":1004,\\\"ruleName\\\":\\\"风控6\\\"},{\\\"ruleId\\\":9,\\\"ruleName\\\":\\\"风控4\\\"},{\\\"ruleId\\\":1003,\\\"ruleName\\\":\\\"风控7\\\"},{\\\"ruleId\\\":6,\\\"ruleName\\\":\\\"风控2\\\"},{\\\"ruleId\\\":8,\\\"ruleName\\\":\\\"风控3\\\"},{\\\"ruleId\\\":5,\\\"ruleName\\\":\\\"风控1\\\"},{\\\"ruleId\\\":1002,\\\"ruleName\\\":\\\"风控8\\\"},{\\\"ruleId\\\":7,\\\"ruleName\\\":\\\"风控9\\\"}],\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"328ms\"}","thrown":""}
{"sysTime":"2025-08-05 11:04:12.691","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"7951e7b9-bcec-4f4a-835f-9d9a26cecf9c","spanId":"0","parentId":"","exportable":"","pid":"8445","thread":"http-nio-20130-exec-1","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":0,\"msg\":\"success\",\"data\":{\"riskTypeList\":[{\"value\":\"1\",\"label\":\"初审\"},{\"value\":\"2\",\"label\":\"复审\"}],\"issuerIdList\":[{\"value\":\"1\",\"label\":\"江苏-苏通卡\"},{\"value\":\"2\",\"label\":\"北京-速通卡\"},{\"value\":\"10\",\"label\":\"天津-速通卡\"},{\"value\":\"25\",\"label\":\"广西-八桂行卡\"},{\"value\":\"26\",\"label\":\"青海-青通卡\"},{\"value\":\"28\",\"label\":\"内蒙古-蒙通卡\"},{\"value\":\"30\",\"label\":\"青海中远-青通卡\"},{\"value\":\"31\",\"label\":\"北京货车-速通卡\"},{\"value\":\"32\",\"label\":\"江苏货车-苏通卡\"},{\"value\":\"33\",\"label\":\"内蒙货车-蒙通卡\"},{\"value\":\"35\",\"label\":\"苏通卡-出租车\"},{\"value\":\"37\",\"label\":\"天津货车-速通卡\"},{\"value\":\"38\",\"label\":\"网路智联\"},{\"value\":\"39\",\"label\":\"江苏9901-苏通卡\"},{\"value\":\"98\",\"label\":\"江苏货车-运政卡\"}],\"riskReviewStatusList\":[{\"value\":\"0\",\"label\":\"待审核\"},{\"value\":\"1\",\"label\":\"审核通过\"},{\"value\":\"2\",\"label\":\"审核驳回取消\"},{\"value\":\"3\",\"label\":\"审核驳回重新上传\"},{\"value\":\"4\",\"label\":\"补传资料\"},{\"value\":\"5\",\"label\":\"审核取消\"}],\"autoAuditList\":[{\"value\":\"0\",\"label\":\"人工审核\"},{\"value\":\"1\",\"label\":\"系统审核\"}],\"rejectReasonList\":[{\"label\":\"驳回并取消\",\"value\":\"risk_review_reject_cancel\",\"children\":[{\"label\":\"您上传的证件存...","thrown":""}
{"sysTime":"2025-08-05 11:04:12.888","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-08-05 11:04:12.889","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}, Instance{instanceId='************#20130#DEFAULT#apply@@delivery-application', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-08-05 11:04:12.889","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(2) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000},{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-08-05 11:04:34.570","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"d703c9a7-ad37-4ec0-90ae-81cee295f2b8","spanId":"0","parentId":"","exportable":"","pid":"8445","thread":"http-nio-20130-exec-2","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"21f80b68883fc1a873fffed5b8e6611d\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-select-option\",\"params\":{}}","thrown":""}
{"sysTime":"2025-08-05 11:04:34.893","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"d703c9a7-ad37-4ec0-90ae-81cee295f2b8","spanId":"0","parentId":"","exportable":"","pid":"8445","thread":"http-nio-20130-exec-2","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=RiskFeign, name=RiskFeign, url=http://delivery-application:20130)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/risk/getRuleByParams\\\\\\\"}, produces={}, value={\\\\\\\"/risk/getRuleByParams\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"queryDTO\\\"]\",\"args\":\"[{\\\"businessType\\\":1,\\\"ruleType\\\":3}]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":[{\\\"ruleId\\\":10,\\\"ruleName\\\":\\\"风控5\\\"},{\\\"ruleId\\\":1004,\\\"ruleName\\\":\\\"风控6\\\"},{\\\"ruleId\\\":6,\\\"ruleName\\\":\\\"风控2\\\"},{\\\"ruleId\\\":8,\\\"ruleName\\\":\\\"风控3\\\"},{\\\"ruleId\\\":9,\\\"ruleName\\\":\\\"风控4\\\"},{\\\"ruleId\\\":1002,\\\"ruleName\\\":\\\"风控8\\\"},{\\\"ruleId\\\":7,\\\"ruleName\\\":\\\"风控9\\\"},{\\\"ruleId\\\":1003,\\\"ruleName\\\":\\\"风控7\\\"},{\\\"ruleId\\\":5,\\\"ruleName\\\":\\\"风控1\\\"}],\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"134ms\"}","thrown":""}
{"sysTime":"2025-08-05 11:04:34.895","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"d703c9a7-ad37-4ec0-90ae-81cee295f2b8","spanId":"0","parentId":"","exportable":"","pid":"8445","thread":"http-nio-20130-exec-2","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":0,\"msg\":\"success\",\"data\":{\"riskTypeList\":[{\"value\":\"1\",\"label\":\"初审\"},{\"value\":\"2\",\"label\":\"复审\"}],\"issuerIdList\":[{\"value\":\"1\",\"label\":\"江苏-苏通卡\"},{\"value\":\"2\",\"label\":\"北京-速通卡\"},{\"value\":\"10\",\"label\":\"天津-速通卡\"},{\"value\":\"25\",\"label\":\"广西-八桂行卡\"},{\"value\":\"26\",\"label\":\"青海-青通卡\"},{\"value\":\"28\",\"label\":\"内蒙古-蒙通卡\"},{\"value\":\"30\",\"label\":\"青海中远-青通卡\"},{\"value\":\"31\",\"label\":\"北京货车-速通卡\"},{\"value\":\"32\",\"label\":\"江苏货车-苏通卡\"},{\"value\":\"33\",\"label\":\"内蒙货车-蒙通卡\"},{\"value\":\"35\",\"label\":\"苏通卡-出租车\"},{\"value\":\"37\",\"label\":\"天津货车-速通卡\"},{\"value\":\"38\",\"label\":\"网路智联\"},{\"value\":\"39\",\"label\":\"江苏9901-苏通卡\"},{\"value\":\"98\",\"label\":\"江苏货车-运政卡\"}],\"riskReviewStatusList\":[{\"value\":\"0\",\"label\":\"待审核\"},{\"value\":\"1\",\"label\":\"审核通过\"},{\"value\":\"2\",\"label\":\"审核驳回取消\"},{\"value\":\"3\",\"label\":\"审核驳回重新上传\"},{\"value\":\"4\",\"label\":\"补传资料\"},{\"value\":\"5\",\"label\":\"审核取消\"}],\"autoAuditList\":[{\"value\":\"0\",\"label\":\"人工审核\"},{\"value\":\"1\",\"label\":\"系统审核\"}],\"rejectReasonList\":[{\"label\":\"驳回并取消\",\"value\":\"risk_review_reject_cancel\",\"children\":[{\"label\":\"您上传的证件存...","thrown":""}
{"sysTime":"2025-08-05 11:08:21.784","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-08-05 11:08:21.785","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-08-05 11:08:21.788","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 11:08:21.788","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 11:08:21.811","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:95","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-95","message":"De-registering from Nacos Server now...","thrown":""}
{"sysTime":"2025-08-05 11:08:21.812","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:100","methodName":"com.alibaba.nacos.client.naming:removeBeatInfo-100","message":"[BEAT] removing beat: apply@@delivery-application:************:20130 from beat map.","thrown":""}
{"sysTime":"2025-08-05 11:08:21.812","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:268","methodName":"com.alibaba.nacos.client.naming:deregisterService-268","message":"[DEREGISTER-SERVICE] test deregistering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}","thrown":""}
{"sysTime":"2025-08-05 11:08:21.849","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:115","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-115","message":"De-registration finished.","thrown":""}
{"sysTime":"2025-08-05 11:08:21.850","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:08:24.577","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:248","methodName":"com.alibaba.nacos.client.naming:processServiceJson-248","message":"removed ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-08-05 11:08:24.577","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-08-05 11:08:24.578","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatTimeOut\":15000,\"instanceHeartBeatInterval\":5000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-08-05 11:08:24.866","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:08:24.867","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:08:27.872","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:08:30.890","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:136","methodName":"com.alibaba.nacos.client.naming:shutdown-136","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:08:30.890","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:08:30.891","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:134","methodName":"com.alibaba.nacos.client.naming:shutdown-134","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:08:30.891","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:428","methodName":"com.alibaba.nacos.client.naming:shutdown-428","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:08:30.891","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:783","methodName":"com.alibaba.nacos.client.naming:shutdown-783","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin","thrown":""}
{"sysTime":"2025-08-05 11:08:30.891","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:72","methodName":"com.alibaba.nacos.client.naming:shutdown-72","message":"{\"msg\":\"[NamingHttpClientManager] Start destroying NacosRestTemplate\"}","thrown":""}
{"sysTime":"2025-08-05 11:08:30.891","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:79","methodName":"com.alibaba.nacos.client.naming:shutdown-79","message":"{\"msg\":\"[NamingHttpClientManager] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-08-05 11:08:30.891","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialWatcher:105","methodName":"c.a.n.client.identify.CredentialWatcher:stop-105","message":"[null] CredentialWatcher is stopped","thrown":""}
{"sysTime":"2025-08-05 11:08:30.892","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialService:98","methodName":"c.a.n.client.identify.CredentialService:free-98","message":"[null] CredentialService is freed","thrown":""}
{"sysTime":"2025-08-05 11:08:30.892","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:787","methodName":"com.alibaba.nacos.client.naming:shutdown-787","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop","thrown":""}
{"sysTime":"2025-08-05 11:08:31.577","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-37","className":"com.xxl.rpc.remoting.net.Server:74","methodName":"com.xxl.rpc.remoting.net.Server:run-74","message":">>>>>>>>>>> xxl-rpc remoting server stop.","thrown":""}
{"sysTime":"2025-08-05 11:08:31.656","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:87","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-87","message":">>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='etc-java-delivery-executor-test', registryValue='**********:20132'}, registryResult:ReturnT [code=200, msg=null, content=null]","thrown":""}
{"sysTime":"2025-08-05 11:08:31.657","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:105","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-105","message":">>>>>>>>>>> xxl-job, executor registry thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:08:31.657","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.xxl.rpc.remoting.net.Server:110","methodName":"com.xxl.rpc.remoting.net.Server:stop-110","message":">>>>>>>>>>> xxl-rpc remoting server destroy success.","thrown":""}
{"sysTime":"2025-08-05 11:08:31.657","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"xxl-job, executor JobLogFileCleanThread","className":"c.x.j.core.thread.JobLogFileCleanThread:99","methodName":"c.x.j.core.thread.JobLogFileCleanThread:run-99","message":">>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:08:31.658","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"xxl-job, executor TriggerCallbackThread","className":"c.x.j.core.thread.TriggerCallbackThread:96","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-96","message":">>>>>>>>>>> xxl-job, executor callback thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:08:31.658","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"Thread-36","className":"c.x.j.core.thread.TriggerCallbackThread:126","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-126","message":">>>>>>>>>>> xxl-job, executor retry callback thread destory.","thrown":""}
{"sysTime":"2025-08-05 11:08:31.807","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-08-05 11:08:31.811","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:08:31.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-08-05 11:08:31.817","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:08:31.817","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-08-05 11:08:31.817","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:08:31.817","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-08-05 11:08:31.818","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-08-05 11:08:31.818","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-08-05 11:08:31.818","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"8445","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
