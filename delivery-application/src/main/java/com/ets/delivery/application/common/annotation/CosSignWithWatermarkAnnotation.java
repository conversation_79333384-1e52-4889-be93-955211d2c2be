package com.ets.delivery.application.common.annotation;

import java.lang.annotation.*;

/**
 * COS图片签名加水印注解
 * 基于CosSignAnnotation功能，在图片URL签名的基础上添加水印参数
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CosSignWithWatermarkAnnotation {

    /**
     * 水印文本内容
     * @return 水印文本
     */
    String watermarkText() default "";

    /**
     * 水印位置
     * @return 水印位置 (topLeft, topRight, bottomLeft, bottomRight, center)
     */
    String watermarkPosition() default "bottomRight";

    /**
     * 水印透明度
     * @return 透明度值 (0-100)
     */
    int watermarkOpacity() default 50;

    /**
     * 水印字体大小
     * @return 字体大小
     */
    int watermarkFontSize() default 16;

    /**
     * 水印颜色
     * @return 颜色值 (十六进制，如: #FFFFFF)
     */
    String watermarkColor() default "#FFFFFF";

    /**
     * 是否启用水印
     * @return 是否启用
     */
    boolean enableWatermark() default true;

    /**
     * 自定义参数，用于扩展功能
     * @return 自定义参数字符串
     */
    String customParams() default "";

}
