package com.ets.delivery.application.common.advice;

import com.ets.base.feign.feign.CosFeign;
import com.ets.base.feign.request.CosGetSignDTO;
import com.ets.base.feign.response.PrivateCosVO;
import com.ets.common.JsonResult;
import com.ets.delivery.application.common.annotation.CosSignWithWatermarkAnnotation;
import com.ets.starter.advice.AbstractCosSignResponseBodyAdvice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * COS图片签名加水印响应体处理器
 * 继承AbstractCosSignResponseBodyAdvice的功能，并添加水印参数处理
 */
@ControllerAdvice
@Slf4j
public class CosSignWithWatermarkResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    @Autowired
    private CosFeign cosFeign;

    @Autowired
    private CosSignResponseBodyAdvice cosSignResponseBodyAdvice;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 检查方法或类是否有CosSignWithWatermarkAnnotation注解
        Method method = returnType.getMethod();
        if (method != null) {
            // 检查方法级别的注解
            if (method.isAnnotationPresent(CosSignWithWatermarkAnnotation.class)) {
                return true;
            }
            // 检查类级别的注解
            if (method.getDeclaringClass().isAnnotationPresent(CosSignWithWatermarkAnnotation.class)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        try {
            // 获取注解信息
            CosSignWithWatermarkAnnotation annotation = getAnnotation(returnType);
            if (annotation == null || !annotation.enableWatermark()) {
                // 如果没有注解或者水印未启用，则使用原有的CosSign逻辑
                return cosSignResponseBodyAdvice.beforeBodyWrite(body, returnType, selectedContentType, 
                    selectedConverterType, request, response);
            }

            // 先执行原有的CosSign逻辑
            Object processedBody = cosSignResponseBodyAdvice.beforeBodyWrite(body, returnType, selectedContentType, 
                selectedConverterType, request, response);

            // 然后添加水印参数处理
            return addWatermarkParams(processedBody, annotation);

        } catch (Exception e) {
            log.error("CosSignWithWatermark处理异常: {}", e.getMessage(), e);
            // 异常时返回原始数据
            return body;
        }
    }

    /**
     * 获取注解信息
     */
    private CosSignWithWatermarkAnnotation getAnnotation(MethodParameter returnType) {
        Method method = returnType.getMethod();
        if (method != null) {
            // 优先获取方法级别的注解
            CosSignWithWatermarkAnnotation annotation = method.getAnnotation(CosSignWithWatermarkAnnotation.class);
            if (annotation != null) {
                return annotation;
            }
            // 获取类级别的注解
            return method.getDeclaringClass().getAnnotation(CosSignWithWatermarkAnnotation.class);
        }
        return null;
    }

    /**
     * 添加水印参数到URL
     */
    private Object addWatermarkParams(Object body, CosSignWithWatermarkAnnotation annotation) {
        if (body == null) {
            return null;
        }

        // 构建水印参数
        Map<String, String> watermarkParams = buildWatermarkParams(annotation);
        
        // 这里你可以实现具体的URL参数拼接逻辑
        // 由于具体的URL处理逻辑需要根据你的业务需求来实现，这里提供一个框架
        return processUrlsWithWatermark(body, watermarkParams);
    }

    /**
     * 构建水印参数
     */
    private Map<String, String> buildWatermarkParams(CosSignWithWatermarkAnnotation annotation) {
        Map<String, String> params = new HashMap<>();
        
        if (!annotation.watermarkText().isEmpty()) {
            params.put("watermark_text", annotation.watermarkText());
        }
        
        params.put("watermark_position", annotation.watermarkPosition());
        params.put("watermark_opacity", String.valueOf(annotation.watermarkOpacity()));
        params.put("watermark_font_size", String.valueOf(annotation.watermarkFontSize()));
        params.put("watermark_color", annotation.watermarkColor());
        
        if (!annotation.customParams().isEmpty()) {
            params.put("custom_params", annotation.customParams());
        }
        
        log.debug("构建水印参数: {}", params);
        return params;
    }

    /**
     * 处理URL添加水印参数
     * 这个方法需要根据你的具体需求来实现
     */
    private Object processUrlsWithWatermark(Object body, Map<String, String> watermarkParams) {
        // TODO: 在这里实现你的具体URL参数拼接逻辑
        // 例如：遍历响应体中的URL字段，为每个URL添加水印参数
        
        log.info("为响应体添加水印参数: {}", watermarkParams);
        
        // 这里返回处理后的body，你需要根据实际情况实现URL参数拼接
        return body;
    }
}
