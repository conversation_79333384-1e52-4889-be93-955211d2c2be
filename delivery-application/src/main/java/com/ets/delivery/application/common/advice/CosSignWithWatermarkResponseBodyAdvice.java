package com.ets.delivery.application.common.advice;

import com.ets.delivery.application.common.annotation.CosSignWithWatermarkAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;

/**
 * COS图片签名加水印响应体处理器
 * 继承AbstractCosSignResponseBodyAdvice的功能，并添加水印参数处理
 */
@ControllerAdvice
@Slf4j
public class CosSignWithWatermarkResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    @Autowired
    private CosFeign cosFeign;

    @Autowired
    private CosSignResponseBodyAdvice cosSignResponseBodyAdvice;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 检查方法是否有CosSignWithWatermarkAnnotation注解
        Method method = returnType.getMethod();
        return method != null && method.isAnnotationPresent(CosSignWithWatermarkAnnotation.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        try {
            // 获取注解信息
            CosSignWithWatermarkAnnotation annotation = getAnnotation(returnType);
            if (annotation == null) {
                return body;
            }

            // 直接添加水印参数处理，不使用原有的CosSign逻辑
            return addWatermarkParams(body, annotation.value());

        } catch (Exception e) {
            log.error("CosSignWithWatermark处理异常: {}", e.getMessage(), e);
            // 异常时返回原始数据
            return body;
        }
    }

    /**
     * 获取注解信息
     */
    private CosSignWithWatermarkAnnotation getAnnotation(MethodParameter returnType) {
        Method method = returnType.getMethod();
        if (method != null) {
            return method.getAnnotation(CosSignWithWatermarkAnnotation.class);
        }
        return null;
    }

    /**
     * 添加水印参数到URL
     */
    private Object addWatermarkParams(Object body, String customParams) {
        if (body == null) {
            return null;
        }

        // TODO: 在这里实现你的具体URL参数拼接逻辑
        // 例如：遍历响应体中的URL字段，为每个URL添加水印参数

        log.info("为响应体添加水印参数: {}", customParams);

        // 这里返回处理后的body，你需要根据实际情况实现URL参数拼接
        return body;
    }
}
