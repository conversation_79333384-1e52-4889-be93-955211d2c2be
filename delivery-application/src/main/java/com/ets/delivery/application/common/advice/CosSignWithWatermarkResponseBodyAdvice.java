package com.ets.delivery.application.common.advice;

import com.ets.base.feign.feign.CosFeign;
import com.ets.base.feign.request.CosGetSignDTO;
import com.ets.base.feign.response.PrivateCosVO;
import com.ets.common.JsonResult;
import com.ets.delivery.application.common.annotation.CosSignWithWatermarkAnnotation;
import com.ets.starter.advice.AbstractCosSignResponseBodyAdvice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * COS图片签名加水印响应体处理器
 * 继承AbstractCosSignResponseBodyAdvice的功能，并添加水印参数处理
 */
@ControllerAdvice
@Slf4j
public class CosSignWithWatermarkResponseBodyAdvice extends AbstractCosSignResponseBodyAdvice {

    @Autowired
    private CosFeign cosFeign;

    @Value("${cos.watermark.base64:}")
    private String watermarkBase64;

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        // 检查方法是否有CosSignWithWatermarkAnnotation注解
        Method method = returnType.getMethod();
        return method != null && method.isAnnotationPresent(CosSignWithWatermarkAnnotation.class);
    }

    @Override
    protected HashMap<String, String> getSignedUrls(HashMap<String, String> matchedUrls) {
        // 先添加水印参数
        HashMap<String, String> watermarkUrls = new HashMap<>();
        for (Map.Entry<String, String> entry : matchedUrls.entrySet()) {
            String originalUrl = entry.getValue();
            String watermarkUrl = appendWatermarkParams(originalUrl);
            watermarkUrls.put(entry.getKey(), watermarkUrl);
        }

        // 然后获取签名URL
        CosGetSignDTO dto = new CosGetSignDTO();
        dto.setUrls(watermarkUrls);

        JsonResult<PrivateCosVO> result = cosFeign.getSignUrl(dto);

        log.info("先添加水印参数，再获取签名URL");
        return result.getDataWithCheckError().getUrls();
    }

    /**
     * 为URL添加水印参数
     */
    private String appendWatermarkParams(String url) {
        if (url == null || watermarkBase64 == null || watermarkBase64.isEmpty()) {
            return url;
        }

        // 按照格式拼接水印参数: watermark/1/image/{watermarkBase64}/batch/1
        String watermarkParams = "watermark/1/image/" + watermarkBase64 + "/batch/1";

        String separator = url.contains("?") ? "?" : "";
        return url + separator + watermarkParams;
    }
}
