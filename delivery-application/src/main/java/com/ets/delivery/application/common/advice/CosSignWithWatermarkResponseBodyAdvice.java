package com.ets.delivery.application.common.advice;

import com.ets.base.feign.feign.CosFeign;
import com.ets.base.feign.request.CosGetSignDTO;
import com.ets.base.feign.response.PrivateCosVO;
import com.ets.common.JsonResult;
import com.ets.delivery.application.common.annotation.CosSignWithWatermarkAnnotation;
import com.ets.starter.advice.AbstractCosSignResponseBodyAdvice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * COS图片签名加水印响应体处理器
 * 继承AbstractCosSignResponseBodyAdvice的功能，并添加水印参数处理
 */
@ControllerAdvice
@Slf4j
public class CosSignWithWatermarkResponseBodyAdvice extends AbstractCosSignResponseBodyAdvice {

    @Autowired
    private CosFeign cosFeign;

    private static final ThreadLocal<String> WATERMARK_PARAMS = new ThreadLocal<>();

    @Override
    protected boolean shouldProcess(MethodParameter returnType) {
        // 检查方法是否有CosSignWithWatermarkAnnotation注解
        Method method = returnType.getMethod();
        if (method != null && method.isAnnotationPresent(CosSignWithWatermarkAnnotation.class)) {
            // 将注解参数存储到ThreadLocal中
            CosSignWithWatermarkAnnotation annotation = method.getAnnotation(CosSignWithWatermarkAnnotation.class);
            WATERMARK_PARAMS.set(annotation.value());
            return true;
        }
        return false;
    }

    @Override
    protected HashMap<String, String> getSignedUrls(HashMap<String, String> matchedUrls) {
        // 先获取签名URL
        CosGetSignDTO dto = new CosGetSignDTO();
        dto.setUrls(matchedUrls);

        JsonResult<PrivateCosVO> result = cosFeign.getSignUrl(dto);
        HashMap<String, String> signedUrls = result.getDataWithCheckError().getUrls();

        // 然后添加水印参数
        return addWatermarkParams(signedUrls);
    }

    /**
     * 添加水印参数到URL
     */
    private HashMap<String, String> addWatermarkParams(HashMap<String, String> signedUrls) {
        // 获取当前请求的注解参数
        String customParams = getCurrentWatermarkParams();

        if (customParams == null || customParams.isEmpty()) {
            return signedUrls;
        }

        HashMap<String, String> watermarkUrls = new HashMap<>();
        for (Map.Entry<String, String> entry : signedUrls.entrySet()) {
            String originalUrl = entry.getValue();
            String watermarkUrl = appendWatermarkParams(originalUrl, customParams);
            watermarkUrls.put(entry.getKey(), watermarkUrl);
        }

        log.info("为URL添加水印参数: {}", customParams);
        return watermarkUrls;
    }

    /**
     * 获取当前请求的水印参数
     */
    private String getCurrentWatermarkParams() {
        try {
            return WATERMARK_PARAMS.get();
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            WATERMARK_PARAMS.remove();
        }
    }

    /**
     * 为URL添加水印参数
     */
    private String appendWatermarkParams(String url, String customParams) {
        if (url == null || customParams == null || customParams.isEmpty()) {
            return url;
        }

        String separator = url.contains("?") ? "&" : "?";
        return url + separator + customParams;
    }
}
